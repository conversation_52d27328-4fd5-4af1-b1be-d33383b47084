package com.sdwu.kotlin.data.repository

import com.sdwu.kotlin.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random
import kotlin.random.Random

/**
 * ECG数据仓库
 * 管理心电图数据的获取、存储和分析
 */
class ECGRepository {
    
    /**
     * 获取实时心电图数据流
     */
    fun getRealtimeECGData(sessionId: String): Flow<ECGRealtimeData> = flow {
        // 模拟实时ECG数据流
        var heartRate = Random.nextInt(60, 100)

        while (true) {
            val currentTime = System.currentTimeMillis()
            val voltage = generateECGVoltage(currentTime)

            // 偶尔改变心率
            if (Random.nextFloat() < 0.1f) {
                heartRate = (heartRate + Random.nextInt(-5, 6)).coerceIn(50, 120)
            }

            val realtimeData = ECGRealtimeData(
                sessionId = sessionId,
                currentDataPoint = ECGDataPoint(
                    timestamp = currentTime,
                    voltage = voltage,
                    leadType = ECGLeadType.LEAD_II
                ),
                heartRate = heartRate,
                signalQuality = ECGQuality.values().random(),
                isConnected = Random.nextFloat() > 0.1f, // 90%连接率
                batteryLevel = Random.nextInt(20, 101),
                timestamp = currentTime
            )

            emit(realtimeData)
            delay(100) // 每100ms发送一次数据
        }
    }
    
    /**
     * 获取历史心电图数据
     */
    suspend fun getHistoricalECGData(
        patientId: String,
        startTime: Long,
        endTime: Long
    ): List<ECGWaveformData> {
        delay(500) // 模拟网络延迟

        val dataList = mutableListOf<ECGWaveformData>()
        val dayInMillis = 24 * 60 * 60 * 1000L
        var currentTime = startTime

        while (currentTime <= endTime && dataList.size < 10) { // 最多返回10条记录
            dataList.add(generateMockECGData(patientId, currentTime))
            currentTime += dayInMillis
        }

        return dataList
    }
    
    /**
     * 获取最新的心电图数据（用于首页展示）
     */
    suspend fun getLatestECGData(patientId: String): ECGWaveformData? {
        delay(300) // 模拟网络延迟
        
        // 生成模拟的最新心电图数据
        return generateMockECGData(patientId)
    }
    
    /**
     * 获取心电图分析结果
     */
    suspend fun getAnalysisResult(waveformId: String): ECGAnalysisResult? {
        delay(300)

        return ECGAnalysisResult(
            waveformId = waveformId,
            heartRateVariability = Random.nextFloat() * 50 + 20,
            averageHeartRate = Random.nextInt(60, 100),
            rhythmType = ECGRhythmType.values().random(),
            qrsWidth = Random.nextFloat() * 50 + 80,
            prInterval = Random.nextFloat() * 80 + 120,
            qtInterval = Random.nextFloat() * 100 + 350,
            stElevation = Random.nextFloat() * 2 - 1,
            tWaveAmplitude = Random.nextFloat() * 0.5f + 0.1f,
            analysisTimestamp = System.currentTimeMillis(),
            confidence = Random.nextFloat() * 0.3f + 0.7f
        )
    }
    
    /**
     * 开始心电图测量会话
     */
    suspend fun startMeasurementSession(patientId: String): String {
        delay(100)
        return "ecg_session_${System.currentTimeMillis()}"
    }

    /**
     * 停止心电图测量会话
     */
    suspend fun stopMeasurementSession(sessionId: String): Boolean {
        delay(100)
        return true
    }
    
    /**
     * 获取心电图统计数据（用于首页卡片展示）
     */
    suspend fun getECGStats(patientId: String): ECGStats {
        delay(200)
        
        return ECGStats(
            averageHeartRate = Random.nextInt(60, 100),
            minHeartRate = Random.nextInt(50, 70),
            maxHeartRate = Random.nextInt(90, 120),
            totalRecordings = Random.nextInt(10, 50),
            lastRecordingTime = System.currentTimeMillis() - Random.nextLong(3600000), // 1小时内
            signalQuality = ECGQuality.values().random()
        )
    }
    
    /**
     * 生成模拟心电图数据
     */
    private fun generateMockECGData(patientId: String): ECGWaveformData {
        val dataPoints = mutableListOf<ECGDataPoint>()
        val samplingRate = 250 // 250Hz
        val duration = 10000L // 10秒
        val startTime = System.currentTimeMillis()
        
        for (i in 0 until (duration * samplingRate / 1000).toInt()) {
            val timestamp = startTime + (i * 1000L / samplingRate)
            val voltage = generateECGVoltage(timestamp)
            
            dataPoints.add(
                ECGDataPoint(
                    timestamp = timestamp,
                    voltage = voltage,
                    leadType = ECGLeadType.LEAD_II
                )
            )
        }
        
        return ECGWaveformData(
            id = "ecg_${System.currentTimeMillis()}",
            patientId = patientId,
            dataPoints = dataPoints,
            samplingRate = samplingRate,
            duration = duration,
            recordedAt = startTime,
            heartRate = Random.nextInt(60, 100),
            quality = ECGQuality.GOOD
        )
    }
    
    /**
     * 生成模拟心电图电压值
     */
    private fun generateECGVoltage(timestamp: Long): Float {
        val t = (timestamp % 1000) / 1000.0 // 1秒周期
        val heartbeatPhase = (t * 2 * PI).toFloat()
        
        // 简化的心电图波形：P波 + QRS波群 + T波
        return when {
            heartbeatPhase < 0.5 -> 0.1f * sin(heartbeatPhase * 4).toFloat() // P波
            heartbeatPhase < 1.5 -> {
                val qrsPhase = (heartbeatPhase - 0.5) * 6
                when {
                    qrsPhase < 1 -> -0.2f * sin(qrsPhase * PI).toFloat() // Q波
                    qrsPhase < 2 -> 1.2f * sin((qrsPhase - 1) * PI).toFloat() // R波
                    else -> -0.3f * sin((qrsPhase - 2) * PI).toFloat() // S波
                }
            }
            heartbeatPhase < 4.0 -> 0.3f * sin((heartbeatPhase - 1.5) * 2).toFloat() // T波
            else -> 0.0f
        }
    }
}

/**
 * ECG统计数据
 * 用于首页展示
 */
data class ECGStats(
    val averageHeartRate: Int,      // 平均心率
    val minHeartRate: Int,          // 最低心率
    val maxHeartRate: Int,          // 最高心率
    val totalRecordings: Int,       // 总记录数
    val lastRecordingTime: Long,    // 最后记录时间
    val signalQuality: ECGQuality   // 信号质量
)
