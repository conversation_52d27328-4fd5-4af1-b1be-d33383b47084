#com.sdwu.kotlin.DataBindingActivity!com.sdwu.kotlin.KotlinApplicationcom.sdwu.kotlin.MainActivity'com.sdwu.kotlin.TraditionalViewActivity#com.sdwu.kotlin.ViewBindingActivity6com.sdwu.kotlin.data.repository.InMemoryUserRepository"com.sdwu.kotlin.utils.CrashHandler.com.sdwu.kotlin.viewmodel.DataBindingViewModel)com.sdwu.kotlin.viewmodel.DetailViewModel'com.sdwu.kotlin.viewmodel.HomeViewModel*com.sdwu.kotlin.viewmodel.ProfileViewModel+com.sdwu.kotlin.viewmodel.SettingsViewModel6com.sdwu.kotlin.databinding.ActivityDataBindingBinding6com.sdwu.kotlin.databinding.ActivityViewBindingBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 