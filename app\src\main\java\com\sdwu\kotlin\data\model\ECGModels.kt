package com.sdwu.kotlin.data.model

/**
 * 心电图导联类型
 */
enum class ECGLeadType {
    LEAD_I,      // 导联I
    LEAD_II,     // 导联II
    LEAD_III,    // 导联III
    AVR,         // aVR导联
    AVL,         // aVL导联
    AVF,         // aVF导联
    V1,          // V1导联
    V2,          // V2导联
    V3,          // V3导联
    V4,          // V4导联
    V5,          // V5导联
    V6           // V6导联
}

/**
 * 心电图信号质量
 */
enum class ECGQuality {
    EXCELLENT,   // 优秀
    GOOD,        // 良好
    FAIR,        // 一般
    POOR,        // 差
    UNUSABLE     // 不可用
}

/**
 * 心电图节律类型
 */
enum class ECGRhythmType {
    NORMAL_SINUS,        // 正常窦性心律
    SINUS_BRADYCARDIA,   // 窦性心动过缓
    SINUS_TACHYCARDIA,   // 窦性心动过速
    ATRIAL_FIBRILLATION, // 心房颤动
    ATRIAL_FLUTTER,      // 心房扑动
    VENTRICULAR_TACHYCARDIA, // 室性心动过速
    VENTRICULAR_FIBRILLATION, // 室颤
    ASYSTOLE,            // 心脏停搏
    UNKNOWN              // 未知
}

/**
 * 心电图数据点
 * 表示单个采样点的数据
 */
data class ECGDataPoint(
    val timestamp: Long,        // 时间戳（毫秒）
    val voltage: Float,         // 电压值（mV）
    val leadType: ECGLeadType   // 导联类型
)

/**
 * 心电图波形数据
 * 表示一段连续的心电图记录
 */
data class ECGWaveformData(
    val id: String,                    // 波形数据ID
    val patientId: String,             // 患者ID
    val dataPoints: List<ECGDataPoint>, // 数据点列表
    val samplingRate: Int,             // 采样率（Hz）
    val duration: Long,                // 持续时间（毫秒）
    val recordedAt: Long,              // 记录时间
    val heartRate: Int,                // 平均心率
    val quality: ECGQuality            // 信号质量
)

/**
 * 实时心电图数据
 * 用于实时数据流传输
 */
data class ECGRealtimeData(
    val sessionId: String,           // 会话ID
    val currentDataPoint: ECGDataPoint, // 当前数据点
    val heartRate: Int,              // 当前心率
    val isConnected: Boolean,        // 设备连接状态
    val signalQuality: ECGQuality,   // 信号质量
    val batteryLevel: Int            // 电池电量（百分比）
)

/**
 * 心电图异常检测结果
 */
data class ECGAbnormality(
    val type: String,           // 异常类型
    val severity: String,       // 严重程度
    val description: String,    // 描述
    val startTime: Long,        // 开始时间
    val endTime: Long,          // 结束时间
    val confidence: Float       // 置信度（0-1）
)

/**
 * 心电图分析结果
 */
data class ECGAnalysisResult(
    val waveformId: String,                    // 波形数据ID
    val heartRateVariability: Float,          // 心率变异性（RMSSD）
    val rhythmType: ECGRhythmType,            // 节律类型
    val abnormalities: List<ECGAbnormality>,  // 异常列表
    val confidence: Float,                     // 整体分析置信度
    val analyzedAt: Long                       // 分析时间
)
